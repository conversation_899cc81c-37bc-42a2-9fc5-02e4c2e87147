#!/usr/bin/env python3
"""
Test the Role serialization fix
"""

import sys
sys.path.insert(0, '.')

def test_role_serialization():
    """Test Role object serialization"""
    print("🧪 Testing Role serialization fix...")
    
    # Mock the structure
    class MockRole:
        def __init__(self):
            self.id = "role_123"
            self.name = "admin"
    
    class MockUser:
        def __init__(self):
            self.id = "user_123"
            self.username = "testuser"
            self.role = MockRole()
    
    class MockUserTenantInfo:
        def __init__(self):
            self.tenant_id = "tenant_123"
            self.user = MockUser()
    
    user_tenant_info = MockUserTenantInfo()
    
    try:
        # Test the fixed serialization
        serialized = {
            "tenant_id": user_tenant_info.tenant_id,
            "user": {
                "id": str(user_tenant_info.user.id),
                "username": user_tenant_info.user.username,
                "role": {
                    "id": str(user_tenant_info.user.role.id),
                    "name": user_tenant_info.user.role.name
                },
                "tenant_id": user_tenant_info.tenant_id
            }
        }
        
        print(f"✅ Serialization successful: {serialized}")
        
        # Test JSON serialization
        import json
        json_str = json.dumps(serialized)
        print(f"✅ JSON serialization successful: {len(json_str)} characters")
        
        # Test deserialization
        deserialized = json.loads(json_str)
        print(f"✅ JSON deserialization successful")
        
        # Verify structure
        assert deserialized["tenant_id"] == "tenant_123"
        assert deserialized["user"]["id"] == "user_123"
        assert deserialized["user"]["username"] == "testuser"
        assert deserialized["user"]["role"]["id"] == "role_123"
        assert deserialized["user"]["role"]["name"] == "admin"
        
        print("🎉 Role serialization fix verified!")
        print("   ✅ Role object properly serialized as dict")
        print("   ✅ JSON serialization works")
        print("   ✅ All fields preserved")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_role_serialization()
    if success:
        print("\n🎯 The error 'Object of type Role is not JSON serializable' should now be fixed!")
    exit(0 if success else 1)
