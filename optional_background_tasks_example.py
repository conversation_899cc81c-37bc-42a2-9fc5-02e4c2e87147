# Example of making BackgroundTasks optional (if you want this approach)

from typing import Optional
from fastapi import BackgroundTasks

@router.post("/execute/{job_id}")
async def execute_job(
    job_id: str,
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin"])),
    background_tasks: Optional[BackgroundTasks] = None
):
    """
    Execute a job using RabbitMQ queue system
    
    Args:
        job_id: Job to execute
        user_tenant_info: User authentication info
        background_tasks: Optional fallback for when RabbitMQ unavailable
    """
    
    # ... validation code ...
    
    try:
        # Try RabbitMQ first
        async with AsyncRabbitMQ(amqp_url=rabbitmq_url) as rabbitmq:
            # ... queue job ...
            return JSONResponse(content={"message": "Job queued for processing"})
            
    except ConnectionError as e:
        # Fallback to background tasks only if provided
        if background_tasks:
            logger.warning(f"RabbitMQ unavailable, falling back to background tasks")
            background_tasks.add_task(process_job_fallback, job_id, user_tenant_info)
            return JSONResponse(content={"message": "Job execution started (fallback mode)"})
        else:
            # No fallback available
            raise HTTPException(
                status_code=503,
                detail="Queue service unavailable and no fallback configured"
            )
