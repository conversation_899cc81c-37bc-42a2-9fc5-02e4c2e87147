#!/usr/bin/env python3
"""
Complete test runner that starts mock API and runs 100 concurrent requests
"""

import asyncio
import subprocess
import time
import logging
import signal
import sys
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestRunner:
    def __init__(self):
        self.mock_server_process = None
        
    async def start_mock_server(self):
        """Start the mock API server"""
        logger.info("🚀 Starting mock API server...")
        
        self.mock_server_process = subprocess.Popen([
            sys.executable, "mock_api_server.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for server to start
        await asyncio.sleep(3)
        logger.info("✅ Mock API server started on port 8301")
        
    def stop_mock_server(self):
        """Stop the mock API server"""
        if self.mock_server_process:
            logger.info("🛑 Stopping mock API server...")
            self.mock_server_process.terminate()
            self.mock_server_process.wait()
            logger.info("✅ Mock API server stopped")
    
    async def run_test(self):
        """Run the complete test"""
        try:
            # Start mock server
            await self.start_mock_server()
            
            # Run the concurrent test
            logger.info("🧪 Running 100 concurrent requests test...")
            
            # Import and run the test
            from test_100_concurrent import main as run_concurrent_test
            result = await run_concurrent_test()
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Test failed: {str(e)}")
            return 1
        finally:
            # Always stop the mock server
            self.stop_mock_server()

async def main():
    """Main function"""
    logger.info("🎯 Complete Concurrent Test Runner")
    logger.info("=" * 60)
    logger.info("This will:")
    logger.info("1. Start a mock API server (10s processing time)")
    logger.info("2. Send 100 concurrent requests")
    logger.info("3. Measure concurrency and performance")
    logger.info("=" * 60)
    
    runner = TestRunner()
    
    # Handle Ctrl+C gracefully
    def signal_handler(sig, frame):
        logger.info("🛑 Received interrupt signal")
        runner.stop_mock_server()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        result = await runner.run_test()
        
        if result == 0:
            logger.info("🎉 Test completed successfully!")
        else:
            logger.error("❌ Test failed!")
            
        return result
        
    except KeyboardInterrupt:
        logger.info("🛑 Test interrupted by user")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
