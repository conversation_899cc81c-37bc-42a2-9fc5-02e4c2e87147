2025-08-05 12:10:21,838 - __main__ - INFO - Starting Job Consumer Service...
2025-08-05 12:10:21,838 - queue_management.async_rabbitmq - INFO - Attempting to connect to RabbitMQ (attempt 1/3)
2025-08-05 12:10:21,853 - aio_pika.robust_connection - INFO - Connection to amqp://guest:******@rabbitmq/ closed. Reconnecting after 5 seconds.
2025-08-05 12:10:21,853 - queue_management.async_rabbitmq - ERROR - Failed to connect to RabbitMQ (attempt 1): [Errno 8] nodename nor servname provided, or not known
2025-08-05 12:10:21,853 - queue_management.async_rabbitmq - INFO - Retrying in 5 seconds...
2025-08-05 12:10:26,855 - queue_management.async_rabbitmq - INFO - Attempting to connect to RabbitMQ (attempt 2/3)
2025-08-05 12:10:26,860 - aio_pika.robust_connection - INFO - Connection to amqp://guest:******@rabbitmq/ closed. Reconnecting after 5 seconds.
2025-08-05 12:10:26,860 - queue_management.async_rabbitmq - ERROR - Failed to connect to RabbitMQ (attempt 2): [Errno 8] nodename nor servname provided, or not known
2025-08-05 12:10:26,860 - queue_management.async_rabbitmq - INFO - Retrying in 5 seconds...
2025-08-05 12:10:31,862 - queue_management.async_rabbitmq - INFO - Attempting to connect to RabbitMQ (attempt 3/3)
2025-08-05 12:10:31,870 - aio_pika.robust_connection - INFO - Connection to amqp://guest:******@rabbitmq/ closed. Reconnecting after 5 seconds.
2025-08-05 12:10:31,871 - queue_management.async_rabbitmq - ERROR - Failed to connect to RabbitMQ (attempt 3): [Errno 8] nodename nor servname provided, or not known
2025-08-05 12:10:31,871 - __main__ - ERROR - Error in consumer service: Failed to connect to RabbitMQ after 3 attempts: [Errno 8] nodename nor servname provided, or not known
2025-08-05 12:10:31,871 - __main__ - ERROR - Failed to start consumer service: Failed to connect to RabbitMQ after 3 attempts: [Errno 8] nodename nor servname provided, or not known
