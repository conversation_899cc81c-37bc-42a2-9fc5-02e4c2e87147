#!/usr/bin/env python3
"""
Test script for 100 concurrent requests to the execute_job endpoint

This script tests the RabbitMQ queue system by sending 100 concurrent requests
directly to the /jobs/execute/{job_id} endpoint.

Credentials:
- Username: superadmin
- Password: godmod
- Client ID: test_tenant

Usage:
    python test_execute_job_concurrent.py
"""

import asyncio
import aiohttp
import time
import json
import logging
from typing import List, Dict, Any
from dataclasses import dataclass

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class TestResult:
    request_id: int
    status_code: int
    response_time: float
    response_data: Dict[Any, Any]
    error: str = None

class ExecuteJobTester:
    def __init__(self, base_url: str = "http://localhost:8300"):
        self.base_url = base_url
        self.session = None
        self.auth_token = None
        self.test_job_ids = []
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        await self.authenticate()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def authenticate(self):
        """Authenticate and get JWT token"""
        try:
            # Prepare form data for authentication
            auth_data = aiohttp.FormData()
            auth_data.add_field('grant_type', 'password')
            auth_data.add_field('username', 'superadmin')
            auth_data.add_field('password', 'godmod')
            auth_data.add_field('scope', '')
            auth_data.add_field('client_id', 'test_tenant')

            logger.info("🔐 Authenticating...")
            async with self.session.post(
                f"{self.base_url}/v1/login",
                data=auth_data
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    self.auth_token = data.get("access_token")
                    logger.info("✅ Authentication successful")
                else:
                    error_text = await response.text()
                    raise Exception(f"Authentication failed: {response.status} - {error_text}")

        except Exception as e:
            logger.error(f"❌ Authentication error: {str(e)}")
            raise
    
    async def get_headers(self):
        """Get headers with authentication"""
        return {
            "Authorization": f"Bearer {self.auth_token}",
            "Content-Type": "application/json"
        }
    
    async def create_test_jobs(self, num_jobs: int = 10):
        """Create test jobs for execution"""
        logger.info(f"📝 Creating {num_jobs} test jobs...")
        
        try:
            # First create a test project
            headers = await self.get_headers()
            project_data = {
                "name": f"Test Project for Queue Testing - {int(time.time())}",
                "description": "Test project for RabbitMQ queue testing"
            }
            
            async with self.session.post(
                f"{self.base_url}/v1/projects/create",
                json=project_data,
                headers=headers
            ) as response:
                if response.status == 200:
                    project = await response.json()
                    project_id = project["_id"]
                    logger.info(f"✅ Created test project: {project_id}")
                else:
                    error_text = await response.text()
                    raise Exception(f"Failed to create project: {response.status} - {error_text}")
            
            # Create test jobs
            for i in range(num_jobs):
                job_data = {
                    "name": f"Test Job {i + 1}",
                    "description": f"Test job {i + 1} for queue testing",
                    "process_name": "generic-entity-extraction"
                }
                
                async with self.session.post(
                    f"{self.base_url}/v1/jobs/create/{project_id}",
                    json=job_data,
                    headers=headers
                ) as response:
                    if response.status == 200:
                        job = await response.json()
                        job_id = job["_id"]
                        self.test_job_ids.append(job_id)
                        
                        # Add a test media item to the job
                        media_data = aiohttp.FormData()
                        media_data.add_field('urls', 'https://httpbin.org/json')
                        
                        async with self.session.post(
                            f"{self.base_url}/v1/jobs/{job_id}/media",
                            data=media_data,
                            headers={"Authorization": f"Bearer {self.auth_token}"}
                        ) as media_response:
                            if media_response.status != 200:
                                logger.warning(f"⚠️ Failed to add media to job {job_id}")
                    else:
                        logger.warning(f"⚠️ Failed to create job {i + 1}")
            
            logger.info(f"✅ Created {len(self.test_job_ids)} test jobs")
            return self.test_job_ids
            
        except Exception as e:
            logger.error(f"❌ Error creating test jobs: {str(e)}")
            raise
    
    async def check_queue_status(self):
        """Check RabbitMQ queue status"""
        try:
            headers = await self.get_headers()
            async with self.session.get(
                f"{self.base_url}/v1/jobs/queue/status",
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"📊 Queue Status: {data}")
                    return data
                else:
                    logger.warning(f"⚠️ Could not get queue status: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"❌ Error checking queue status: {str(e)}")
            return None
    
    async def execute_job_request(self, request_id: int, job_id: str) -> TestResult:
        """Send a single execute job request"""
        start_time = time.time()
        
        try:
            headers = await self.get_headers()
            
            async with self.session.post(
                f"{self.base_url}/v1/jobs/execute/{job_id}",
                headers=headers
            ) as response:
                response_time = time.time() - start_time
                response_data = await response.json()
                
                return TestResult(
                    request_id=request_id,
                    status_code=response.status,
                    response_time=response_time,
                    response_data=response_data
                )
                
        except Exception as e:
            response_time = time.time() - start_time
            return TestResult(
                request_id=request_id,
                status_code=0,
                response_time=response_time,
                response_data={},
                error=str(e)
            )
    
    async def run_concurrent_test(self, num_requests: int = 100):
        """Run concurrent execute job requests test"""
        logger.info(f"🚀 Starting {num_requests} concurrent execute_job requests test...")
        
        # Create test jobs if we don't have enough
        if len(self.test_job_ids) < 10:
            await self.create_test_jobs(10)
        
        if not self.test_job_ids:
            raise Exception("No test jobs available")
        
        # Check initial queue status
        await self.check_queue_status()
        
        # Create tasks for concurrent requests (reuse job IDs cyclically)
        tasks = []
        for i in range(num_requests):
            job_id = self.test_job_ids[i % len(self.test_job_ids)]
            task = asyncio.create_task(
                self.execute_job_request(i + 1, job_id)
            )
            tasks.append(task)
        
        # Execute all requests concurrently
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        # Process results
        successful_requests = []
        failed_requests = []
        queue_full_requests = []
        
        for result in results:
            if isinstance(result, Exception):
                failed_requests.append(str(result))
            elif isinstance(result, TestResult):
                if result.status_code == 200:
                    successful_requests.append(result)
                elif result.status_code == 503:  # Service Unavailable (queue full)
                    queue_full_requests.append(result)
                else:
                    failed_requests.append(result)
        
        # Print results
        logger.info("=" * 60)
        logger.info("📊 EXECUTE JOB TEST RESULTS")
        logger.info("=" * 60)
        logger.info(f"Total requests: {num_requests}")
        logger.info(f"Total time: {total_time:.2f} seconds")
        logger.info(f"Requests per second: {num_requests / total_time:.2f}")
        logger.info(f"✅ Successful requests: {len(successful_requests)}")
        logger.info(f"🚫 Queue full responses: {len(queue_full_requests)}")
        logger.info(f"❌ Failed requests: {len(failed_requests)}")
        
        # Show response time statistics
        if successful_requests:
            response_times = [r.response_time for r in successful_requests]
            avg_response_time = sum(response_times) / len(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
            
            logger.info(f"⏱️ Response time stats:")
            logger.info(f"   Average: {avg_response_time:.3f}s")
            logger.info(f"   Min: {min_response_time:.3f}s")
            logger.info(f"   Max: {max_response_time:.3f}s")
        
        # Show sample responses
        if queue_full_requests:
            logger.info("🚫 Sample queue full response:")
            sample = queue_full_requests[0]
            logger.info(f"   {sample.response_data}")
        
        # Check final queue status
        logger.info("📊 Final queue status:")
        await self.check_queue_status()
        
        return {
            "total_requests": num_requests,
            "successful": len(successful_requests),
            "queue_full": len(queue_full_requests),
            "failed": len(failed_requests),
            "total_time": total_time,
            "requests_per_second": num_requests / total_time
        }

async def main():
    """Main test function"""
    logger.info("🧪 RabbitMQ Execute Job Concurrent Test")
    logger.info("=" * 60)
    
    try:
        async with ExecuteJobTester() as tester:
            # Test with 100 concurrent requests
            results = await tester.run_concurrent_test(num_requests=100)
            
            logger.info("=" * 60)
            logger.info("🎯 TEST SUMMARY")
            logger.info("=" * 60)
            logger.info(f"The RabbitMQ queue system handled:")
            logger.info(f"✅ {results['successful']} successful job executions")
            logger.info(f"🚫 {results['queue_full']} queue full responses")
            logger.info(f"❌ {results['failed']} failed requests")
            logger.info(f"⚡ {results['requests_per_second']:.2f} requests per second")
            
            if results['queue_full'] > 0:
                logger.info("✅ Queue full mechanism is working correctly!")
                logger.info("   This means the queue reached its 50 message limit")
                logger.info("   and correctly rejected additional requests.")
            
            if results['successful'] > 0:
                logger.info("✅ Job queuing is working correctly!")
                logger.info("   Jobs are being queued for processing by RabbitMQ.")
                
    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
