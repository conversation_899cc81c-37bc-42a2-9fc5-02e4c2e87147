#!/usr/bin/env python3
"""
Quick test to verify the serialization fix works
"""

import sys
sys.path.insert(0, '.')

from queue_management.async_rabbitmq import serialize_user_tenant_info, deserialize_user_tenant_info
from app.models.user import User, UserTenantDB
from app.models.role import Role

# Mock objects for testing
class MockDB:
    pass

class MockAsyncDB:
    pass

def test_serialization():
    print("🧪 Testing User serialization fix...")
    
    # Create a mock Role object
    role = Role(
        _id="role_id_123",
        name="admin"
    )

    # Create a mock User object (as it appears in the actual system)
    user = User(
        _id="test_user_id_123",
        username="testuser",
        role=role
    )
    
    # Create a mock UserTenantDB object
    user_tenant_info = UserTenantDB(
        tenant_id="test_tenant_123",
        db=MockDB(),
        user=user,
        async_db=MockAsyncDB()
    )
    
    try:
        # Test serialization
        print("📤 Testing serialization...")
        serialized = serialize_user_tenant_info(user_tenant_info)
        print(f"✅ Serialization successful: {serialized}")
        
        # Test deserialization
        print("📥 Testing deserialization...")
        deserialized = deserialize_user_tenant_info(serialized)
        print(f"✅ Deserialization successful!")
        print(f"   Tenant ID: {deserialized.tenant_id}")
        print(f"   User ID: {deserialized.user.id}")
        print(f"   Username: {deserialized.user.username}")
        print(f"   Role: {deserialized.user.role}")
        
        print("🎉 Serialization fix working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_serialization()
    exit(0 if success else 1)
