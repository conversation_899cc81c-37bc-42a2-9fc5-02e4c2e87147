#!/usr/bin/env python3
"""
Example usage of the RabbitMQ Queue Management System

This script demonstrates how to use the new queue-based job execution system.

Prerequisites:
1. RabbitMQ server running (docker compose up -d rabbitmq)
2. MongoDB server running
3. Valid job ID in the database

Usage:
    python queue_management/example_usage.py
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from queue_management.async_rabbitmq import AsyncRabbitMQ, serialize_user_tenant_info

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def example_queue_operations():
    """
    Example of basic queue operations
    """
    logger.info("=== Example: Basic Queue Operations ===")
    
    try:
        # Initialize RabbitMQ connection
        rabbitmq_url = "amqp://guest:guest@rabbitmq/"
        
        async with AsyncRabbitMQ(amqp_url=rabbitmq_url) as rabbitmq:
            logger.info("✅ Connected to RabbitMQ")
            
            # Get initial queue status
            queue_info = await rabbitmq.get_queue_info()
            logger.info(f"📊 Initial queue status: {queue_info}")
            
            # Example user tenant data (normally this comes from your authentication system)
            example_user_data = {
                "tenant_id": "example_tenant_123",
                "user": {
                    "id": "user_456",
                    "username": "example_user",
                    "role": "admin",
                    "tenant_id": "example_tenant_123"
                }
            }
            
            # Publish a job to the queue
            job_id = "example_job_789"
            success = await rabbitmq.publish_job(job_id, example_user_data)
            
            if success:
                logger.info(f"✅ Successfully queued job: {job_id}")
            else:
                logger.warning(f"⚠️ Failed to queue job: {job_id} (queue might be full)")
            
            # Get updated queue status
            queue_info = await rabbitmq.get_queue_info()
            logger.info(f"📊 Updated queue status: {queue_info}")
            
    except Exception as e:
        logger.error(f"❌ Error in queue operations: {str(e)}")


async def example_queue_monitoring():
    """
    Example of queue monitoring and status checking
    """
    logger.info("=== Example: Queue Monitoring ===")
    
    try:
        rabbitmq_url = "amqp://guest:guest@rabbitmq/"
        
        async with AsyncRabbitMQ(amqp_url=rabbitmq_url) as rabbitmq:
            queue_info = await rabbitmq.get_queue_info()
            
            # Calculate utilization percentages
            message_count = queue_info.get("message_count", 0)
            max_length = queue_info.get("max_length", 50)
            consumer_count = queue_info.get("consumer_count", 0)
            max_concurrent = queue_info.get("max_concurrent_jobs", 20)
            
            queue_utilization = (message_count / max_length) * 100
            consumer_utilization = (consumer_count / max_concurrent) * 100
            
            logger.info(f"📈 Queue Metrics:")
            logger.info(f"   Messages in queue: {message_count}/{max_length} ({queue_utilization:.1f}%)")
            logger.info(f"   Active consumers: {consumer_count}/{max_concurrent} ({consumer_utilization:.1f}%)")
            
            # Health status
            if queue_utilization > 90:
                logger.warning("⚠️ Queue is nearly full!")
            elif queue_utilization > 70:
                logger.info("📊 Queue utilization is high")
            else:
                logger.info("✅ Queue utilization is healthy")
                
    except Exception as e:
        logger.error(f"❌ Error in queue monitoring: {str(e)}")


async def example_api_usage():
    """
    Example of how to use the new API endpoints
    """
    logger.info("=== Example: API Usage ===")
    
    logger.info("🔗 New API Endpoints:")
    logger.info("   POST /v1/jobs/execute-queue/{job_id} - Queue a job for processing")
    logger.info("   GET /v1/jobs/queue/status - Get queue status")
    
    logger.info("📝 Example API calls:")
    logger.info("""
    # Queue a job for processing
    curl -X POST "http://localhost:8300/v1/jobs/execute-queue/your_job_id" \\
         -H "Authorization: Bearer your_token"
    
    # Check queue status
    curl -X GET "http://localhost:8300/v1/jobs/queue/status" \\
         -H "Authorization: Bearer your_token"
    """)
    
    logger.info("📋 Response examples:")
    logger.info("""
    Success (job queued):
    {
        "message": "Job queued for processing",
        "job_id": "your_job_id",
        "status": "in_progress",
        "queue_info": {
            "position_in_queue": 3,
            "max_concurrent_jobs": 20,
            "queue_capacity": 50
        }
    }
    
    Queue full:
    {
        "message": "Please try again, service is busy",
        "job_id": "your_job_id",
        "status": "queue_full",
        "details": "Maximum queue capacity reached. Please try again later."
    }
    """)


async def example_consumer_setup():
    """
    Example of how to set up and run the consumer service
    """
    logger.info("=== Example: Consumer Service Setup ===")
    
    logger.info("🚀 To start processing jobs from the queue:")
    logger.info("   python queue_management/consumer.py")
    
    logger.info("🔧 Environment variables needed:")
    logger.info("   RABBITMQ_URL=amqp://guest:guest@rabbitmq/")
    logger.info("   MONGO_URI=mongodb://your_mongo_connection")
    
    logger.info("📊 The consumer service will:")
    logger.info("   - Connect to RabbitMQ and start consuming jobs")
    logger.info("   - Process up to 20 jobs concurrently")
    logger.info("   - Handle job failures gracefully")
    logger.info("   - Send webhook notifications when configured")
    logger.info("   - Log all processing activities")


async def main():
    """
    Run all examples
    """
    logger.info("🚀 RabbitMQ Queue Management System Examples")
    logger.info("=" * 50)
    
    # Example 1: Basic queue operations
    await example_queue_operations()
    await asyncio.sleep(1)
    
    # Example 2: Queue monitoring
    await example_queue_monitoring()
    await asyncio.sleep(1)
    
    # Example 3: API usage
    await example_api_usage()
    await asyncio.sleep(1)
    
    # Example 4: Consumer setup
    await example_consumer_setup()
    
    logger.info("=" * 50)
    logger.info("🎉 Examples completed!")
    logger.info("💡 Next steps:")
    logger.info("   1. Start RabbitMQ: docker compose up -d rabbitmq")
    logger.info("   2. Start consumer: python queue_management/consumer.py")
    logger.info("   3. Use the new API endpoints to queue jobs")


if __name__ == "__main__":
    asyncio.run(main())
