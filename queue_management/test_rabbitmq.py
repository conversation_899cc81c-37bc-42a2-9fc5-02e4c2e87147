#!/usr/bin/env python3
"""
Test script for RabbitMQ functionality

This script tests the basic functionality of the AsyncRabbitMQ class
without requiring a full application setup.

Usage:
    python queue_management/test_rabbitmq.py
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from queue_management.async_rabbitmq import AsyncRabbitMQ

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_rabbitmq_connection():
    """
    Test basic RabbitMQ connection and queue operations
    """
    try:
        logger.info("Testing RabbitMQ connection...")
        
        # Test connection with default URL
        rabbitmq_url = "amqp://guest:guest@rabbitmq/"
        
        async with AsyncRabbitMQ(amqp_url=rabbitmq_url) as rabbitmq:
            logger.info("✅ Successfully connected to RabbitMQ")
            
            # Test queue info
            queue_info = await rabbitmq.get_queue_info()
            logger.info(f"✅ Queue info retrieved: {queue_info}")
            
            # Test publishing a dummy job
            dummy_user_data = {
                "tenant_id": "test_tenant",
                "user": {
                    "id": "test_user_id",
                    "username": "test_user",
                    "role": "admin",
                    "tenant_id": "test_tenant"
                }
            }
            
            success = await rabbitmq.publish_job("test_job_123", dummy_user_data)
            if success:
                logger.info("✅ Successfully published test job to queue")
            else:
                logger.warning("⚠️ Failed to publish job (queue might be full)")
            
            # Get updated queue info
            queue_info = await rabbitmq.get_queue_info()
            logger.info(f"✅ Updated queue info: {queue_info}")
            
        logger.info("✅ RabbitMQ connection test completed successfully")
        return True
        
    except ConnectionError as e:
        logger.error(f"❌ Connection error: {str(e)}")
        logger.info("💡 Make sure RabbitMQ is running (docker compose up -d rabbitmq)")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error: {str(e)}")
        return False


async def test_queue_full_scenario():
    """
    Test queue full scenario by publishing many messages
    """
    try:
        logger.info("Testing queue full scenario...")
        
        rabbitmq_url = "amqp://guest:guest@rabbitmq/"
        
        async with AsyncRabbitMQ(amqp_url=rabbitmq_url) as rabbitmq:
            dummy_user_data = {
                "tenant_id": "test_tenant",
                "user": {
                    "id": "test_user_id",
                    "username": "test_user",
                    "role": "admin",
                    "tenant_id": "test_tenant"
                }
            }
            
            # Try to publish many jobs to test queue limits
            published_count = 0
            for i in range(60):  # Try to publish more than max queue length (50)
                success = await rabbitmq.publish_job(f"test_job_{i}", dummy_user_data)
                if success:
                    published_count += 1
                else:
                    logger.info(f"✅ Queue full scenario triggered after {published_count} messages")
                    break
            
            queue_info = await rabbitmq.get_queue_info()
            logger.info(f"✅ Final queue info: {queue_info}")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in queue full test: {str(e)}")
        return False


async def main():
    """
    Run all tests
    """
    logger.info("🚀 Starting RabbitMQ tests...")
    
    # Test 1: Basic connection and operations
    test1_success = await test_rabbitmq_connection()
    
    if test1_success:
        # Test 2: Queue full scenario (only if basic test passed)
        test2_success = await test_queue_full_scenario()
        
        if test1_success and test2_success:
            logger.info("🎉 All tests passed!")
            return 0
    
    logger.error("❌ Some tests failed")
    return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
