#!/usr/bin/env python3
"""
RabbitMQ Consumer Service for Job Processing

This service runs as a background process to consume jobs from the RabbitMQ queue
and process them using the existing job processing logic.

Usage:
    python queue_management/consumer.py

Environment Variables:
    RABBITMQ_URL: RabbitMQ connection URL (default: amqp://guest:guest@rabbitmq/)
    MONGO_URI: MongoDB connection URI
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv
from queue_management.async_rabbitmq import AsyncRabbitMQ
from app.v1.api.jobs import process_job_from_queue

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('queue_consumer.log')
    ]
)
logger = logging.getLogger(__name__)


class JobConsumerService:
    """
    Service to consume and process jobs from RabbitMQ queue
    """
    
    def __init__(self, rabbitmq_url: str = None):
        self.rabbitmq_url = rabbitmq_url or os.getenv("RABBITMQ_URL", "amqp://guest:guest@rabbitmq/")
        self.rabbitmq: AsyncRabbitMQ = None
        self.running = False
        
    async def start(self):
        """
        Start the consumer service
        """
        logger.info("Starting Job Consumer Service...")
        
        try:
            # Initialize RabbitMQ connection
            self.rabbitmq = AsyncRabbitMQ(amqp_url=self.rabbitmq_url)
            await self.rabbitmq.connect()
            
            # Start consuming messages
            await self.rabbitmq.start_consumer(process_job_from_queue)
            
            self.running = True
            logger.info("Job Consumer Service started successfully")
            
            # Keep the service running
            while self.running:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Received shutdown signal")
            await self.stop()
        except Exception as e:
            logger.error(f"Error in consumer service: {str(e)}")
            raise
    
    async def stop(self):
        """
        Stop the consumer service gracefully
        """
        logger.info("Stopping Job Consumer Service...")
        self.running = False
        
        if self.rabbitmq:
            await self.rabbitmq.close()
            
        logger.info("Job Consumer Service stopped")


async def main():
    """
    Main function to run the consumer service
    """
    # Validate required environment variables
    if not os.getenv("MONGO_URI"):
        logger.error("MONGO_URI environment variable is required")
        sys.exit(1)
    
    # Create and start the consumer service
    consumer = JobConsumerService()
    
    try:
        await consumer.start()
    except Exception as e:
        logger.error(f"Failed to start consumer service: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    # Run the consumer service
    asyncio.run(main())
