import aio_pika
import json
import asyncio
import logging
from typing import Optional, Callable, Any, Union
from aio_pika import connect_robust, Message, DeliveryMode
from aio_pika.abc import AbstractConnection, AbstractChannel, AbstractQueue

logger = logging.getLogger(__name__)

class AsyncRabbitMQ:
    def __init__(self, amqp_url: str = 'amqp://guest:guest@rabbitmq/', queue_name: str = 'job_processing_queue'):
        self.amqp_url = amqp_url
        self.queue_name = queue_name
        self.connection: Optional[AbstractConnection] = None
        self.channel: Optional[AbstractChannel] = None
        self.queue: Optional[AbstractQueue] = None
        self.max_concurrent_jobs = 20
        self.max_queue_length = 50
        self.retry_attempts = 3
        self.retry_delay = 5  # seconds

    async def __aenter__(self):
        """Async context manager entry"""
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
        return False  # Don't suppress exceptions

    async def connect(self) -> None:
        """
        Establish connection to RabbitMQ server with retry logic
        """
        for attempt in range(self.retry_attempts):
            try:
                logger.info(f"Attempting to connect to RabbitMQ (attempt {attempt + 1}/{self.retry_attempts})")

                # Use connect_robust for automatic reconnection
                self.connection = await connect_robust(
                    self.amqp_url,
                    heartbeat=600,  # 10 minutes heartbeat
                    blocked_connection_timeout=300,  # 5 minutes timeout
                )

                # Create channel
                self.channel = await self.connection.channel()

                # Set QoS to limit concurrent processing
                await self.channel.set_qos(prefetch_count=self.max_concurrent_jobs)

                # Declare the queue with specific arguments
                self.queue = await self.channel.declare_queue(
                    self.queue_name,
                    durable=True,  # Queue survives broker restart
                    arguments={
                        'x-max-length': self.max_queue_length,  # Maximum 50 messages
                        'x-overflow': 'reject-publish',  # Reject new messages when full
                    }
                )

                logger.info(f"Successfully connected to RabbitMQ and declared queue '{self.queue_name}'")
                return

            except Exception as e:
                logger.error(f"Failed to connect to RabbitMQ (attempt {attempt + 1}): {str(e)}")
                if attempt < self.retry_attempts - 1:
                    logger.info(f"Retrying in {self.retry_delay} seconds...")
                    await asyncio.sleep(self.retry_delay)
                else:
                    raise ConnectionError(f"Failed to connect to RabbitMQ after {self.retry_attempts} attempts: {str(e)}")

    async def close(self) -> None:
        """
        Close RabbitMQ connection gracefully
        """
        try:
            if self.connection and not self.connection.is_closed:
                await self.connection.close()
                logger.info("RabbitMQ connection closed successfully")
        except Exception as e:
            logger.error(f"Error closing RabbitMQ connection: {str(e)}")

    async def publish_job(self, job_id: str, user_tenant_info_data: dict) -> bool:
        """
        Publish a job to the queue

        Args:
            job_id: The job ID to process
            user_tenant_info_data: Serialized user tenant information

        Returns:
            bool: True if published successfully, False if queue is full
        """
        try:
            if not self.channel or not self.queue:
                raise RuntimeError("RabbitMQ not connected. Call connect() first.")

            # Prepare message payload
            message_body = {
                "job_id": job_id,
                "user_tenant_info": user_tenant_info_data,
                "timestamp": asyncio.get_event_loop().time()
            }

            # Create message
            message = Message(
                json.dumps(message_body).encode(),
                delivery_mode=DeliveryMode.PERSISTENT,  # Message survives broker restart
                headers={"job_id": job_id}
            )

            # Publish message
            await self.channel.default_exchange.publish(
                message,
                routing_key=self.queue_name,
                mandatory=True  # Ensure message is routed to a queue
            )

            logger.info(f"Job {job_id} published to queue successfully")
            return True

        except aio_pika.exceptions.MessageReturnedException:
            # Queue is full or message was rejected
            logger.warning(f"Queue is full, cannot publish job {job_id}")
            return False
        except Exception as e:
            logger.error(f"Error publishing job {job_id} to queue: {str(e)}")
            raise

    async def start_consumer(self, process_callback: Callable[[str, dict], Any]) -> None:
        """
        Start consuming messages from the queue

        Args:
            process_callback: Async function to process jobs (job_id, user_tenant_info_data)
        """
        try:
            if not self.queue:
                raise RuntimeError("Queue not declared. Call connect() first.")

            async def message_handler(message: aio_pika.IncomingMessage):
                async with message.process():
                    try:
                        # Parse message
                        message_body = json.loads(message.body.decode())
                        job_id = message_body["job_id"]
                        user_tenant_info_data = message_body["user_tenant_info"]

                        logger.info(f"Processing job {job_id} from queue")

                        # Process the job
                        await process_callback(job_id, user_tenant_info_data)

                        logger.info(f"Job {job_id} processed successfully")

                    except Exception as e:
                        logger.error(f"Error processing message: {str(e)}")
                        # Message will be rejected and not requeued due to process() context
                        raise

            # Start consuming
            await self.queue.consume(message_handler)
            logger.info(f"Started consuming from queue '{self.queue_name}' with max {self.max_concurrent_jobs} concurrent jobs")

        except Exception as e:
            logger.error(f"Error starting consumer: {str(e)}")
            raise

    async def get_queue_info(self) -> dict:
        """
        Get information about the queue

        Returns:
            dict: Queue information including message count
        """
        try:
            if not self.queue:
                raise RuntimeError("Queue not declared. Call connect() first.")

            # Declare queue to get current state (passive=True means don't create if not exists)
            queue_info = await self.channel.declare_queue(self.queue_name, passive=True)

            return {
                "name": self.queue_name,
                "message_count": queue_info.declaration_result.message_count,
                "consumer_count": queue_info.declaration_result.consumer_count,
                "max_length": self.max_queue_length,
                "max_concurrent_jobs": self.max_concurrent_jobs
            }

        except Exception as e:
            logger.error(f"Error getting queue info: {str(e)}")
            return {
                "name": self.queue_name,
                "message_count": 0,
                "consumer_count": 0,
                "max_length": self.max_queue_length,
                "max_concurrent_jobs": self.max_concurrent_jobs,
                "error": str(e)
            }


def serialize_user_tenant_info(user_tenant_info) -> dict:
    """
    Serialize UserTenantDB object for queue transmission

    Args:
        user_tenant_info: UserTenantDB object

    Returns:
        dict: Serialized user tenant information
    """
    return {
        "tenant_id": user_tenant_info.tenant_id,
        "user": {
            "id": str(user_tenant_info.user.id),
            "username": user_tenant_info.user.username,
            "role": user_tenant_info.user.role,
            "tenant_id": user_tenant_info.user.tenant_id
        }
    }


def deserialize_user_tenant_info(data: dict):
    """
    Deserialize user tenant information and reconstruct UserTenantDB object

    Args:
        data: Serialized user tenant information

    Returns:
        UserTenantDB: Reconstructed user tenant information
    """
    from app.models.user import User, UserTenantDB
    from app.core.database import get_db_from_tenant_id, get_async_db_from_tenant_id

    # Reconstruct User object
    user = User(
        id=data["user"]["id"],
        username=data["user"]["username"],
        role=data["user"]["role"],
        tenant_id=data["user"]["tenant_id"]
    )

    # Get database connections
    db = get_db_from_tenant_id(data["tenant_id"])
    async_db = get_async_db_from_tenant_id(data["tenant_id"])

    # Reconstruct UserTenantDB object
    return UserTenantDB(
        tenant_id=data["tenant_id"],
        db=db,
        user=user,
        async_db=async_db
    )
