#!/usr/bin/env python3
"""
Simple RabbitMQ Consumer to process the queued jobs

This will consume the 3 jobs currently in your queue and process them.
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from queue_management.async_rabbitmq import AsyncRabbitMQ
from app.v1.api.jobs import process_job_from_queue

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def simple_consumer():
    """Simple consumer to process queued jobs"""
    try:
        logger.info("🚀 Starting simple consumer to process queued jobs...")
        
        rabbitmq_url = "amqp://guest:guest@localhost/"
        
        async with AsyncRabbitMQ(amqp_url=rabbitmq_url) as rabbitmq:
            logger.info("✅ Connected to RabbitMQ")
            
            # Check queue status
            queue_info = await rabbitmq.get_queue_info()
            logger.info(f"📊 Queue has {queue_info.get('message_count', 0)} messages waiting")
            
            if queue_info.get('message_count', 0) == 0:
                logger.info("📭 No messages in queue to process")
                return
            
            # Start consuming
            logger.info("🔄 Starting to consume messages...")
            await rabbitmq.start_consumer(process_job_from_queue)
            
            # Keep running for a while to process messages
            logger.info("⏳ Processing messages for 60 seconds...")
            await asyncio.sleep(60)
            
        logger.info("✅ Consumer finished")
        
    except Exception as e:
        logger.error(f"❌ Consumer error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(simple_consumer())
