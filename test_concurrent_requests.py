#!/usr/bin/env python3
"""
Test script for 100 concurrent requests to the /run API

This script tests the RabbitMQ queue system by sending 100 concurrent requests
to the processes/{process_name}/run endpoint which internally calls execute_job.

Credentials:
- Username: superadmin
- Password: godmod
- Client ID: test_tenant

Usage:
    python test_concurrent_requests.py
"""

import asyncio
import aiohttp
import time
import json
import logging
from typing import List, Dict, Any
from dataclasses import dataclass

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class TestResult:
    request_id: int
    status_code: int
    response_time: float
    response_data: Dict[Any, Any]
    error: str = None

class ConcurrentAPITester:
    def __init__(self, base_url: str = "http://localhost:8300"):
        self.base_url = base_url
        self.session = None
        self.auth_token = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        await self.authenticate()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def authenticate(self):
        """Authenticate and get JWT token"""
        try:
            # Prepare form data for authentication
            auth_data = aiohttp.FormData()
            auth_data.add_field('grant_type', 'password')
            auth_data.add_field('username', 'superadmin')
            auth_data.add_field('password', 'godmod')
            auth_data.add_field('scope', '')
            auth_data.add_field('client_id', 'test_tenant')

            logger.info("🔐 Authenticating...")
            async with self.session.post(
                f"{self.base_url}/v1/login",
                data=auth_data
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    self.auth_token = data.get("access_token")
                    logger.info("✅ Authentication successful")
                else:
                    error_text = await response.text()
                    raise Exception(f"Authentication failed: {response.status} - {error_text}")

        except Exception as e:
            logger.error(f"❌ Authentication error: {str(e)}")
            raise
    
    async def get_headers(self):
        """Get headers with authentication"""
        return {
            "Authorization": f"Bearer {self.auth_token}",
            "Content-Type": "application/json"
        }
    
    async def check_queue_status(self):
        """Check RabbitMQ queue status"""
        try:
            headers = await self.get_headers()
            async with self.session.get(
                f"{self.base_url}/v1/jobs/queue/status",
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"📊 Queue Status: {data}")
                    return data
                else:
                    logger.warning(f"⚠️ Could not get queue status: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"❌ Error checking queue status: {str(e)}")
            return None
    
    async def send_run_request(self, request_id: int, process_name: str = "generic-entity-extraction") -> TestResult:
        """Send a single request to the /run API"""
        start_time = time.time()
        
        try:
            # Prepare form data for the /run endpoint
            data = aiohttp.FormData()
            data.add_field('urls', 'https://httpbin.org/json')  # Simple test URL
            data.add_field('project_name', f'Test Project {request_id}')
            data.add_field('job_name', f'Test Job {request_id}')
            data.add_field('wait_for_completion', 'false')  # Don't wait for completion
            
            headers = {
                "Authorization": f"Bearer {self.auth_token}"
            }
            
            async with self.session.post(
                f"{self.base_url}/v1/processes/{process_name}/run",
                data=data,
                headers=headers
            ) as response:
                response_time = time.time() - start_time
                response_data = await response.json()
                
                return TestResult(
                    request_id=request_id,
                    status_code=response.status,
                    response_time=response_time,
                    response_data=response_data
                )
                
        except Exception as e:
            response_time = time.time() - start_time
            return TestResult(
                request_id=request_id,
                status_code=0,
                response_time=response_time,
                response_data={},
                error=str(e)
            )
    
    async def run_concurrent_test(self, num_requests: int = 100, process_name: str = "generic-entity-extraction"):
        """Run concurrent requests test"""
        logger.info(f"🚀 Starting {num_requests} concurrent requests test...")
        
        # Check initial queue status
        await self.check_queue_status()
        
        # Create tasks for concurrent requests
        tasks = []
        for i in range(num_requests):
            task = asyncio.create_task(
                self.send_run_request(i + 1, process_name)
            )
            tasks.append(task)
        
        # Execute all requests concurrently
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        # Process results
        successful_requests = []
        failed_requests = []
        queue_full_requests = []
        
        for result in results:
            if isinstance(result, Exception):
                failed_requests.append(str(result))
            elif isinstance(result, TestResult):
                if result.status_code == 200:
                    successful_requests.append(result)
                elif result.status_code == 503:  # Service Unavailable (queue full)
                    queue_full_requests.append(result)
                else:
                    failed_requests.append(result)
        
        # Print results
        logger.info("=" * 60)
        logger.info("📊 TEST RESULTS")
        logger.info("=" * 60)
        logger.info(f"Total requests: {num_requests}")
        logger.info(f"Total time: {total_time:.2f} seconds")
        logger.info(f"Requests per second: {num_requests / total_time:.2f}")
        logger.info(f"✅ Successful requests: {len(successful_requests)}")
        logger.info(f"🚫 Queue full responses: {len(queue_full_requests)}")
        logger.info(f"❌ Failed requests: {len(failed_requests)}")
        
        # Show response time statistics
        if successful_requests:
            response_times = [r.response_time for r in successful_requests]
            avg_response_time = sum(response_times) / len(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
            
            logger.info(f"⏱️ Response time stats:")
            logger.info(f"   Average: {avg_response_time:.3f}s")
            logger.info(f"   Min: {min_response_time:.3f}s")
            logger.info(f"   Max: {max_response_time:.3f}s")
        
        # Show queue full responses
        if queue_full_requests:
            logger.info("🚫 Queue full responses:")
            for result in queue_full_requests[:5]:  # Show first 5
                logger.info(f"   Request {result.request_id}: {result.response_data.get('message', 'N/A')}")
            if len(queue_full_requests) > 5:
                logger.info(f"   ... and {len(queue_full_requests) - 5} more")
        
        # Show failed requests
        if failed_requests:
            logger.info("❌ Failed requests:")
            for error in failed_requests[:5]:  # Show first 5
                logger.info(f"   {error}")
            if len(failed_requests) > 5:
                logger.info(f"   ... and {len(failed_requests) - 5} more")
        
        # Check final queue status
        logger.info("📊 Final queue status:")
        await self.check_queue_status()
        
        return {
            "total_requests": num_requests,
            "successful": len(successful_requests),
            "queue_full": len(queue_full_requests),
            "failed": len(failed_requests),
            "total_time": total_time,
            "requests_per_second": num_requests / total_time
        }

async def main():
    """Main test function"""
    logger.info("🧪 RabbitMQ Concurrent Request Test")
    logger.info("=" * 60)
    
    try:
        async with ConcurrentAPITester() as tester:
            # Test with 100 concurrent requests
            results = await tester.run_concurrent_test(
                num_requests=100,
                process_name="generic-entity-extraction"
            )
            
            logger.info("=" * 60)
            logger.info("🎯 TEST SUMMARY")
            logger.info("=" * 60)
            logger.info(f"The RabbitMQ queue system handled:")
            logger.info(f"✅ {results['successful']} successful job submissions")
            logger.info(f"🚫 {results['queue_full']} queue full responses")
            logger.info(f"❌ {results['failed']} failed requests")
            logger.info(f"⚡ {results['requests_per_second']:.2f} requests per second")
            
            if results['queue_full'] > 0:
                logger.info("✅ Queue full mechanism is working correctly!")
            
            if results['successful'] > 0:
                logger.info("✅ Job queuing is working correctly!")
                
    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
