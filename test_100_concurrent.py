#!/usr/bin/env python3
"""
Test script to send 100 concurrent requests to the mock API
"""

import asyncio
import aiohttp
import time
import logging
from dataclasses import dataclass
from typing import List

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class RequestResult:
    request_id: int
    status_code: int
    response_time: float
    response_data: dict
    error: str = None

class ConcurrentTester:
    def __init__(self, base_url: str = "http://localhost:8301"):
        self.base_url = base_url
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def send_request(self, request_id: int) -> RequestResult:
        """Send a single request to the mock API"""
        start_time = time.time()
        
        try:
            async with self.session.post(
                f"{self.base_url}/v1/processes/generic-entity-extraction/run"
            ) as response:
                response_time = time.time() - start_time
                response_data = await response.json()
                
                return RequestResult(
                    request_id=request_id,
                    status_code=response.status,
                    response_time=response_time,
                    response_data=response_data
                )
                
        except Exception as e:
            response_time = time.time() - start_time
            return RequestResult(
                request_id=request_id,
                status_code=0,
                response_time=response_time,
                response_data={},
                error=str(e)
            )
    
    async def run_concurrent_test(self, num_requests: int = 100):
        """Run concurrent requests test"""
        logger.info(f"🚀 Starting {num_requests} concurrent requests test...")
        logger.info(f"📡 Target: {self.base_url}")
        logger.info(f"⏱️ Each request simulates 10 seconds of processing")
        
        # Create tasks for concurrent requests
        tasks = []
        for i in range(num_requests):
            task = asyncio.create_task(self.send_request(i + 1))
            tasks.append(task)
        
        # Execute all requests concurrently
        start_time = time.time()
        logger.info(f"📤 Sending {num_requests} requests simultaneously...")
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        # Process results
        successful_requests = []
        failed_requests = []
        
        for result in results:
            if isinstance(result, Exception):
                failed_requests.append(str(result))
            elif isinstance(result, RequestResult):
                if result.status_code == 200:
                    successful_requests.append(result)
                else:
                    failed_requests.append(result)
        
        # Print results
        logger.info("=" * 60)
        logger.info("📊 CONCURRENT TEST RESULTS")
        logger.info("=" * 60)
        logger.info(f"Total requests: {num_requests}")
        logger.info(f"Total time: {total_time:.2f} seconds")
        logger.info(f"Requests per second: {num_requests / total_time:.2f}")
        logger.info(f"✅ Successful requests: {len(successful_requests)}")
        logger.info(f"❌ Failed requests: {len(failed_requests)}")
        
        # Response time statistics
        if successful_requests:
            response_times = [r.response_time for r in successful_requests]
            avg_response_time = sum(response_times) / len(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
            
            logger.info(f"⏱️ Response time stats:")
            logger.info(f"   Average: {avg_response_time:.2f}s")
            logger.info(f"   Min: {min_response_time:.2f}s")
            logger.info(f"   Max: {max_response_time:.2f}s")
            
            # Show sample responses
            logger.info(f"📋 Sample successful response:")
            sample = successful_requests[0]
            logger.info(f"   Job ID: {sample.response_data.get('job_id', 'N/A')}")
            logger.info(f"   Message: {sample.response_data.get('message', 'N/A')}")
            logger.info(f"   Processing time: {sample.response_data.get('processing_time_seconds', 'N/A')}s")
        
        # Analysis
        logger.info("=" * 60)
        logger.info("🔍 ANALYSIS:")
        
        if total_time < 15:  # Should take ~10 seconds if truly concurrent
            logger.info("✅ Requests processed concurrently!")
            logger.info(f"   Total time ({total_time:.2f}s) is close to individual processing time (10s)")
        else:
            logger.info("⚠️ Requests may have been processed sequentially")
            logger.info(f"   Total time ({total_time:.2f}s) suggests sequential processing")
        
        success_rate = (len(successful_requests) / num_requests) * 100
        logger.info(f"📈 Success rate: {success_rate:.1f}%")
        
        return {
            "total_requests": num_requests,
            "successful": len(successful_requests),
            "failed": len(failed_requests),
            "total_time": total_time,
            "avg_response_time": avg_response_time if successful_requests else 0,
            "success_rate": success_rate
        }

async def main():
    """Main test function"""
    logger.info("🧪 Mock API Concurrent Test")
    logger.info("Testing 100 concurrent requests with 10-second processing simulation")
    logger.info("=" * 60)
    
    try:
        async with ConcurrentTester() as tester:
            # Test with 100 concurrent requests
            results = await tester.run_concurrent_test(num_requests=100)
            
            logger.info("=" * 60)
            logger.info("🎯 FINAL SUMMARY")
            logger.info("=" * 60)
            logger.info(f"✅ {results['successful']}/{results['total_requests']} requests successful")
            logger.info(f"⏱️ Average response time: {results['avg_response_time']:.2f}s")
            logger.info(f"📈 Success rate: {results['success_rate']:.1f}%")
            logger.info(f"🚀 Total test time: {results['total_time']:.2f}s")
            
            if results['total_time'] < 15:
                logger.info("🎉 Concurrent processing confirmed!")
            else:
                logger.info("⚠️ May need to check server concurrency settings")
                
    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
