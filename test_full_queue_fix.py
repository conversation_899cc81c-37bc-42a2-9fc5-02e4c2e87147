#!/usr/bin/env python3
"""
Test the complete queue system with Role serialization fix
"""

import asyncio
import sys
sys.path.insert(0, '.')

from queue_management.async_rabbitmq import AsyncRabbitMQ, serialize_user_tenant_info

async def test_complete_queue_system():
    """Test the complete queue system with proper Role serialization"""
    print("🧪 Testing complete RabbitMQ system with Role fix...")
    
    try:
        async with AsyncRabbitMQ(amqp_url="amqp://guest:guest@localhost/") as rabbitmq:
            print("✅ Connected to RabbitMQ successfully")
            
            # Create mock objects that match the real structure
            class MockRole:
                def __init__(self):
                    self.id = "role_admin_123"
                    self.name = "admin"
            
            class MockUser:
                def __init__(self):
                    self.id = "user_test_456"
                    self.username = "testuser"
                    self.role = MockRole()
            
            class MockUserTenantInfo:
                def __init__(self):
                    self.tenant_id = "test_tenant_789"
                    self.user = MockUser()
            
            user_tenant_info = MockUserTenantInfo()
            
            # Test serialization
            print("📤 Testing serialization...")
            user_data = serialize_user_tenant_info(user_tenant_info)
            print(f"✅ Serialization successful: {user_data}")
            
            # Test publishing to queue
            print("📨 Testing job publishing...")
            success = await rabbitmq.publish_job("test_job_with_role_fix", user_data)
            
            if success:
                print("✅ Job published successfully with Role serialization")
            else:
                print("⚠️ Job publish failed (queue might be full)")
            
            # Get queue info
            queue_info = await rabbitmq.get_queue_info()
            print(f"📊 Queue info: {queue_info}")
            
        print("🎉 Complete test successful!")
        print("   ✅ Role object properly serialized")
        print("   ✅ Job published to RabbitMQ queue")
        print("   ✅ No JSON serialization errors")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    print("🔧 Testing complete RabbitMQ system with fixes...")
    success = await test_complete_queue_system()
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
