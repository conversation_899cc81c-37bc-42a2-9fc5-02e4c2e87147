#!/usr/bin/env python3
"""
Mock API server that simulates the /processes/run endpoint with 10-second processing time
"""

import asyncio
import time
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
import uvicorn
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Mock API Server")

# Global counter for job IDs
job_counter = 0

@app.post("/v1/processes/{process_name}/run")
async def mock_process_run(process_name: str):
    """
    Mock endpoint that simulates the /processes/run API with 10-second processing
    """
    global job_counter
    job_counter += 1
    job_id = f"mock_job_{job_counter}_{int(time.time())}"
    
    start_time = time.time()
    logger.info(f"🚀 Starting mock job {job_id} for process {process_name}")
    
    try:
        # Simulate 10 seconds of processing
        await asyncio.sleep(10)
        
        processing_time = time.time() - start_time
        logger.info(f"✅ Mock job {job_id} completed in {processing_time:.2f} seconds")
        
        return JSONResponse(
            status_code=200,
            content={
                "message": "Mock job completed successfully",
                "job_id": job_id,
                "process_name": process_name,
                "processing_time_seconds": processing_time,
                "status": "completed"
            }
        )
        
    except Exception as e:
        logger.error(f"❌ Mock job {job_id} failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Mock job failed: {str(e)}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "message": "Mock API server is running"}

@app.get("/stats")
async def get_stats():
    """Get mock API statistics"""
    return {
        "total_jobs_processed": job_counter,
        "simulated_processing_time": "10 seconds per job"
    }

if __name__ == "__main__":
    logger.info("🚀 Starting Mock API Server on port 8301...")
    logger.info("📋 Available endpoints:")
    logger.info("   POST /v1/processes/{process_name}/run - Mock process execution (10s delay)")
    logger.info("   GET /health - Health check")
    logger.info("   GET /stats - API statistics")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8301,
        log_level="info"
    )
